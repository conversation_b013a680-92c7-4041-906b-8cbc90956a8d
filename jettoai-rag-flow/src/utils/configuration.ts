import { useProviderStore } from '@/stores/modules/workflow/provider'
import { ModelStatusEnum, ModelTypeEnum } from '@/apis/model-mgmt/type'

export const correctModelProvider = (provider: string) => {
  if (!provider) return ''

  if (provider.includes('/')) return provider

  if (['google'].includes(provider)) return 'langgenius/gemini/google'

  return `langgenius/${provider}/${provider}`
}
export const correctToolProvider = (provider: string, toolInCollectionList?: boolean) => {
  if (!provider) return ''

  if (toolInCollectionList) return provider

  if (provider.includes('/')) return provider

  if (['stepfun', 'jina', 'siliconflow', 'gitee_ai'].includes(provider))
    return `langgenius/${provider}_tool/${provider}`

  return `langgenius/${provider}/${provider}`
}

export const useTextGenerationCurrentProviderAndModelAndModelList = (defaultModel?: any) => {
  const providerStore = useProviderStore()
  const textGenerationModelList = providerStore.provider.textGenerationModelList
  const activeTextGenerationModelList = textGenerationModelList.filter(
    (model) => model.status === ModelStatusEnum.active
  )
  const { currentProvider, currentModel } = useCurrentProviderAndModel(textGenerationModelList, defaultModel)
  return {
    currentProvider,
    currentModel,
    textGenerationModelList,
    activeTextGenerationModelList
  }
}

export const useCurrentProviderAndModel = (modelList, defaultModel) => {
  const currentProvider = modelList.find((provider) => provider.provider === defaultModel?.provider)
  const currentModel = currentProvider?.models.find((model) => model.model === defaultModel?.model)

  return {
    currentProvider,
    currentModel
  }
}
/**
 * 获取模型名称
 * @param obj
 */
export const renderName = (obj: Record<string, string>) => {
  if (!obj) return ''
  if (obj?.['zh_Hans']) return obj['zh_Hans']
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}

export const sizeFormat = (size: number) => {
  const remainder = Math.floor(size / 1000)
  if (remainder < 1) return `${size}`
  else return `${remainder}K`
}
export const modelTypeFormat = (modelType: ModelTypeEnum) => {
  if (modelType === ModelTypeEnum.textEmbedding) return 'TEXT EMBEDDING'

  return modelType.toLocaleUpperCase()
}
export const renderI18nObject = (obj: Record<string, string>, language: string = 'zh_Hans') => {
  if (!obj) return ''
  if (obj?.[language]) return obj[language]
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}
