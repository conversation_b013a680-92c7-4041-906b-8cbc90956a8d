import { defineStore } from 'pinia'
import { getModelTypesHttp, getParamsRulesHttp } from '@/apis/model-mgmt'
import type { Model, SubModelListItem } from '@/apis/model-mgmt/type'
import { ModelTypeEnum } from '@/apis/model-mgmt/type'
import { omit } from 'lodash-es'

export const useProviderStore = defineStore('provider', () => {
  const provider = reactive({
    modelProviders: [],
    paramsList: [],
    completion_params: {},
    currentProvider: {} as Model,
    currentModel: {} as SubModelListItem,
    // LLM
    textGenerationModelList: [] as Model[]
  })
  const getModelList = async (modelType: string) => {
    try {
      const res = await getModelTypesHttp(modelType)
      const result = res.data || []
      result.forEach((item) => {
        item.models.forEach((model) => {
          model.provider = {
            ...omit(item, 'models')
          }
        })
      })
      if (modelType === ModelTypeEnum.textGeneration) {
        provider.textGenerationModelList = result || []
      }
    } catch (error) {
      return Promise.reject(error)
    }
  }
  const getParamsList = async () => {
    const res = await getParamsRulesHttp(provider.currentProvider.provider, provider.currentModel.model)
    console.log('模型的参数配置res：', res)
    provider.paramsList = res.data || []
  }
  return {
    provider,
    getModelList,
    getParamsList
  }
})
