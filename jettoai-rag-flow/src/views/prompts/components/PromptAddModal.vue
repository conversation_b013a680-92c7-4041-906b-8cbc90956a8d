<template>
  <div class="prompt-container">
    <div class="page-header">
      <div class="cursor-pointer">
        <icon-left />
        <span class="pl-2">{{ isEdit ? '编辑提示词' : '新增提示词' }}</span>
      </div>
      <a-button type="primary" @click="handleSubmit">提 交</a-button>
    </div>

    <div class="content-container flex-1" style="overflow-y: auto">
      <div class="left-panel">
        <div class="section-title">基本信息</div>

        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-align="left"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
        >
          <a-form-item field="name" label="Prompt 名称 *" required>
            <a-input v-model="formData.name" placeholder="请输入提示词名称" allow-clear />
          </a-form-item>

          <a-form-item field="promptKey" label="Prompt Key *" required>
            <a-input v-model="formData.promptKey" placeholder="Unique-prompt-key" allow-clear />
          </a-form-item>

          <a-form-item field="type" label="类型 *" required>
            <a-radio-group v-model="formData.type">
              <a-radio :value="1">系统提示词</a-radio>
              <a-radio :value="2">用户提示词</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item field="description" label="描述">
            <a-textarea
              v-model="formData.description"
              placeholder="简要描述这个提示词的用途..."
              allow-clear
              :max-length="200"
              :show-word-limit="true"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>

          <a-form-item field="tags" label="标签">
            <div class="tag-container">
              <div class="tag-input-wrapper">
                <a-input v-model="tagInput" placeholder="添加标签" allow-clear @press-enter="addTag" />
                <div class="tag-add-btn-wrapper">
                  <a-button class="tag-add-btn" type="primary" size="small" @click="addTag">
                    <template #icon>
                      <icon-plus />
                    </template>
                    新增标签
                  </a-button>
                </div>
              </div>
              <div class="tags-wrapper">
                <a-tag
                  v-for="(tag, index) in tagList"
                  :key="index"
                  closable
                  color="#EAEBF0"
                  :color-style="{ color: '#3B3D4D', borderColor: 'transparent' }"
                  @close="removeTag(index)"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </div>
          </a-form-item>

          <a-form-item field="content" label="Prompt 内容" required>
            <a-textarea
              v-model="formData.content"
              placeholder="输入你的提示词内容，支持使用{{VARIABLE NAME}}格式的变量"
              allow-clear
              :max-length="5000"
              :show-word-limit="true"
              :auto-size="{ minRows: 8, maxRows: 15 }"
            />
            <div class="prompt-tips">提示:使用 {{}}变量名称{{}}的格式来定义可替换的变量</div>
          </a-form-item>

          <a-form-item field="helpInfo" label="帮助信息">
            <a-textarea
              v-model="formData.helpInfo"
              placeholder="添加使用说明或注意事项"
              allow-clear
              :max-length="500"
              :show-word-limit="true"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>
      </div>

      <div class="right-panel">
        <div class="section-wrapper">
          <div class="section-title">模型配置</div>

          <a-form
            :model="modelConfig"
            label-align="left"
            :label-col-props="{ span: 8 }"
            :wrapper-col-props="{ span: 16 }"
          >
            <a-form-item field="model" label="选择模型">
              <a-select v-model="modelConfig.model" placeholder="选择模型">
                <template #prefix>
                  <div class="model-icon">
                    <div class="model-icon-inner" />
                  </div>
                </template>
                <a-option value="gpt-3.5-turbo">豆包·1.5·Pro·32k</a-option>
                <a-option value="gpt-4">GPT-4</a-option>
                <a-option value="claude-3">Claude-3</a-option>
              </a-select>
            </a-form-item>

            <a-form-item field="maxTokens" label="最大回复长度">
              <div class="slider-container">
                <div class="slider-value">{{ modelConfig.maxTokens }}</div>
                <a-slider v-model="modelConfig.maxTokens" :min="0" :max="8192" :step="1" show-ticks />
                <div class="slider-handle-container">
                  <div class="slider-handle" :style="{ left: `${(modelConfig.maxTokens / 8192) * 100}%` }" />
                </div>
              </div>
            </a-form-item>

            <a-form-item field="temperature" label="生成随机性(Temperature)">
              <div class="slider-container">
                <div class="slider-value">{{ modelConfig.temperature }}</div>
                <a-slider v-model="modelConfig.temperature" :min="0" :max="2" :step="0.1" show-ticks />
                <div class="slider-handle-container">
                  <div class="slider-handle" :style="{ left: `${(modelConfig.temperature / 2) * 100}%` }" />
                </div>
              </div>
            </a-form-item>

            <a-form-item field="topP" label="Top P">
              <div class="slider-container">
                <div class="slider-value">{{ modelConfig.topP }}</div>
                <a-slider v-model="modelConfig.topP" :min="0" :max="1" :step="0.1" show-ticks />
                <div class="slider-handle-container">
                  <div class="slider-handle" :style="{ left: `${modelConfig.topP * 100}%` }" />
                </div>
              </div>
            </a-form-item>
          </a-form>
        </div>

        <div class="section-wrapper">
          <div class="section-title">版本信息</div>

          <div class="version-info">
            <div class="version-row">
              <span class="version-label">当前版本</span>
              <span class="version-value">{{ versionInfo.version || 'V1.0' }}</span>
            </div>
            <div class="version-row">
              <span class="version-label">创建者</span>
              <span class="version-value">{{ versionInfo.creator || '系统管理员' }}</span>
            </div>
            <div class="version-row">
              <span class="version-label">创建时间</span>
              <span class="version-value">{{ versionInfo.createTime || '2023-06-10 10:23:54' }}</span>
            </div>
          </div>
        </div>

        <div class="section-wrapper">
          <div class="section-title">预览与调试</div>

          <div class="preview-container">
            <div class="conversation-blocks">
              <div class="conversation-block">
                <div class="block-header">
                  <div class="block-header-left">
                    <div class="drag-handle">
                      <icon-drag />
                    </div>
                    <span class="block-type">User</span>
                  </div>
                  <div class="block-actions">
                    <a-tooltip content="排序" position="top">
                      <icon-sort />
                    </a-tooltip>
                    <a-tooltip content="收起" position="top">
                      <icon-up />
                    </a-tooltip>
                    <a-tooltip content="删除" position="top">
                      <icon-delete />
                    </a-tooltip>
                  </div>
                </div>
                <a-textarea
                  v-model="previewData.userMessage"
                  placeholder="请输入问题测试大模型回复，回车发送，Shift+回车换行"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                />
              </div>

              <div class="conversation-block">
                <div class="block-header">
                  <div class="block-header-left">
                    <div class="drag-handle">
                      <icon-drag />
                    </div>
                    <span class="block-type">Assistant</span>
                  </div>
                  <div class="block-actions">
                    <a-tooltip content="排序" position="top">
                      <icon-sort />
                    </a-tooltip>
                    <a-tooltip content="收起" position="top">
                      <icon-up />
                    </a-tooltip>
                    <a-tooltip content="删除" position="top">
                      <icon-delete />
                    </a-tooltip>
                  </div>
                </div>
                <a-textarea
                  v-model="previewData.assistantMessage"
                  placeholder="经分析得出市场规模增长快、竞争激烈但小米品牌和技术有优势"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                />
              </div>

              <div class="conversation-block">
                <div class="block-header">
                  <div class="block-header-left">
                    <div class="drag-handle">
                      <icon-drag />
                    </div>
                    <span class="block-type">Placeholder</span>
                  </div>
                  <div class="block-actions">
                    <a-tooltip content="排序" position="top">
                      <icon-sort />
                    </a-tooltip>
                    <a-tooltip content="收起" position="top">
                      <icon-up />
                    </a-tooltip>
                    <a-tooltip content="删除" position="top">
                      <icon-delete />
                    </a-tooltip>
                  </div>
                </div>
                <a-textarea
                  v-model="previewData.placeholder"
                  placeholder="占位符内容..."
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                />
              </div>

              <div class="add-block-button">
                <a-dropdown trigger="click" position="bottom">
                  <a-button type="outline" size="small">
                    <template #icon>
                      <icon-plus />
                    </template>
                    添加提示词块
                  </a-button>
                  <template #content>
                    <a-doption @click="addConversationBlock('user')">
                      <template #icon>
                        <icon-user />
                      </template>
                      <template #default>User</template>
                    </a-doption>
                    <a-doption @click="addConversationBlock('assistant')">
                      <template #icon>
                        <icon-robot />
                      </template>
                      <template #default>Assistant</template>
                    </a-doption>
                    <a-doption @click="addConversationBlock('system')">
                      <template #icon>
                        <icon-computer />
                      </template>
                      <template #default>System</template>
                    </a-doption>
                    <a-doption @click="addConversationBlock('placeholder')">
                      <template #icon>
                        <icon-question-circle />
                      </template>
                      <template #default>Placeholder</template>
                    </a-doption>
                  </template>
                </a-dropdown>
              </div>
            </div>

            <div class="preview-actions">
              <a-button type="primary" class="run-button" @click="runPreview">
                <template #icon>
                  <icon-play />
                </template>
                运行
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import { useRouter, useRoute } from 'vue-router'
import { saveUsingPost, updateUsingPut, getUsingGet } from '@/apis/prompts'
import type { Prompt, PromptSaveDTO, PromptUpdateDTO } from '@/apis/prompts/type'

defineOptions({ name: 'PromptAddPage' })

const router = useRouter()
const route = useRoute()

const promptId = computed(() => route.params.id as string | undefined)
const isEdit = computed(() => !!promptId.value)

const formRef = ref<FormInstance>()

// 扩展PromptSaveDTO类型，添加UI需要的字段
interface ExtendedPromptDTO extends PromptSaveDTO {
  promptKey?: string
  id?: string
  helpInfo?: string
}

const formData = reactive<ExtendedPromptDTO>({
  name: '',
  promptKey: '',
  type: 1,
  content: '',
  tags: '',
  description: '',
  helpInfo: '',
  status: 1
})

// 模型配置
const modelConfig = reactive({
  model: 'gpt-3.5-turbo',
  maxTokens: 4096,
  temperature: 0.7,
  topP: 0.9
})

// 版本信息
const versionInfo = reactive({
  version: '',
  creator: '',
  createTime: ''
})

// 预览数据
const previewData = reactive({
  userMessage: '',
  assistantMessage: '',
  placeholder: ''
})

// 会话块列表
const conversationBlocks = ref([
  { type: 'user', content: '' },
  { type: 'assistant', content: '' },
  { type: 'placeholder', content: '' }
])

// 添加新的会话块
const addConversationBlock = (type: string) => {
  conversationBlocks.value.push({
    type,
    content: ''
  })
  Message.success(`已添加${type}类型块`)
}

// 标签相关
const tagInput = ref('')
const tagList = ref<string[]>([])

// 添加标签
const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !tagList.value.includes(tag)) {
    tagList.value.push(tag)
    updateFormDataTags()
    tagInput.value = ''
  }
}

// 删除标签
const removeTag = (index: number) => {
  tagList.value.splice(index, 1)
  updateFormDataTags()
}

// 更新表单数据中的tags字段
const updateFormDataTags = () => {
  formData.tags = tagList.value.join(',')
}

// 从表单数据中解析标签
const parseTagsFromFormData = () => {
  if (formData.tags) {
    tagList.value = formData.tags.split(',').filter((tag) => tag.trim() !== '')
  } else {
    tagList.value = []
  }
}

const rules = {
  name: [{ required: true, message: '请输入提示词名称' }],
  promptKey: [{ required: true, message: '请输入提示词Key' }],
  type: [{ required: true, message: '请选择提示词类型' }],
  content: [{ required: true, message: '请输入提示词内容' }]
}

const resetForm = () => {
  formData.name = ''
  formData.promptKey = ''
  formData.type = 1
  formData.content = ''
  formData.tags = ''
  formData.description = ''
  formData.helpInfo = ''
  formData.status = 1
  tagList.value = []
  tagInput.value = ''

  // 重置模型配置
  modelConfig.model = 'gpt-3.5-turbo'
  modelConfig.maxTokens = 4096
  modelConfig.temperature = 0.7
  modelConfig.topP = 0.9

  // 重置预览数据
  previewData.userMessage = ''
  previewData.assistantMessage = ''
  previewData.placeholder = ''
}

// 获取详情
const getDetail = async (id: string) => {
  try {
    const res = await getUsingGet(id)
    if (res.data) {
      const { name, promptKey, type, content, tags, description, status } = res.data as Prompt

      // 填充表单数据
      formData.name = name || ''
      formData.promptKey = promptKey || ''
      formData.type = type || 1
      formData.content = content || ''
      formData.tags = tags || ''
      formData.description = description || ''
      formData.helpInfo = '' // 接口可能不返回此字段，使用description或默认空值
      formData.status = status || 1
      formData.id = id

      // 解析标签
      parseTagsFromFormData()

      // 填充版本信息
      if (res.data.createTime) {
        versionInfo.createTime = res.data.createTime
      }
      if (res.data.createdBy) {
        versionInfo.creator = res.data.createdBy
      }
      versionInfo.version = res.data.version || 'V1.0'
    }
  } catch (error) {
    console.error('获取提示词详情失败', error)
    Message.error('获取提示词详情失败')
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      return
    }

    try {
      // 从formData中提取API所需的字段
      const { name, type, content, tags, description, status } = formData
      const saveData: PromptSaveDTO = { name, type, content, tags, description, status }

      if (isEdit.value && promptId.value) {
        // 编辑
        await updateUsingPut({
          ...saveData,
          id: promptId.value
        })
        Message.success('更新成功')
      } else {
        // 新增
        await saveUsingPost(saveData)
        Message.success('创建成功')
      }

      // 返回列表页
      router.push('/prompts')
    } catch (error) {
      console.error('保存失败', error)
      Message.error('保存失败')
    }
  })
}

// 取消操作
const handleCancel = () => {
  router.push('/prompts')
}

// 运行预览
const runPreview = () => {
  if (!previewData.userMessage) {
    Message.warning('请输入测试问题')
    return
  }

  // 这里可以接入实际的API调用预览提示词效果
  // 暂时使用模拟响应
  previewData.assistantMessage = '根据您的提示词，这是一个模拟的回复内容。实际效果取决于您配置的模型和参数。'
  Message.success('预览请求已发送')
}

// 组件挂载时初始化
onMounted(() => {
  if (isEdit.value && promptId.value) {
    getDetail(promptId.value)
  }
})
</script>

<style scoped lang="scss">
.prompt-container {
  background-color: #fff;
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
  }
  .prompt-container-layout {
    display: flex;
    background-color: #f7f6fd;
    flex-direction: row;
    border-top: 1px solid #e9ebf2;
    border-bottom: 1px solid #e9ebf2;
    &-left {
      flex: 1;
      padding: 8px 16px;
    }
    &-right {
      width: 400px;
      padding: 8px 16px;
      border-left: 1px solid #e9ebf2;
    }
  }
}

.content-container {
  display: flex;
  gap: 24px;
}

.left-panel {
  flex: 1;
  min-width: 0;
}

.right-panel {
  width: 360px;
  flex-shrink: 0;
}

.section-wrapper {
  margin-bottom: 24px;
  border: 1px solid #c9c9d6;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #242431;
  margin-bottom: 16px;
}

.tag-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.tag-add-btn-wrapper {
  flex-shrink: 0;
}

.tag-add-btn {
  background-color: #5147ff;
  color: #fff;
  border: none;

  &:hover {
    background-color: #3b34c5;
  }
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.prompt-tips {
  margin-top: 8px;
  font-size: 14px;
  color: #b6b6bf;
}

.model-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #37e1be 0%, #a569ff 35%, #1e37fc 100%);
  border-radius: 4px;
  margin-right: 8px;

  .model-icon-inner {
    width: 6px;
    height: 8px;
    background-color: #fff;
    clip-path: polygon(0 0, 100% 50%, 0 100%);
  }
}

.slider-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;

  .slider-value {
    text-align: right;
    color: #242431;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .slider-handle-container {
    position: relative;
    height: 12px;
    margin-top: -16px;
    pointer-events: none;
  }

  .slider-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: #4d53e8;
    border-radius: 50%;
    top: 0;
    transform: translateX(-50%);
    z-index: 10;
  }
}

.version-info {
  .version-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .version-label {
      color: #b6b6bf;
      font-size: 14px;
    }

    .version-value {
      color: #242431;
      font-size: 14px;
      text-align: right;
    }
  }
}

.conversation-blocks {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.conversation-block {
  border: 1px solid #c9c9d6;
  border-radius: 8px;
  overflow: hidden;

  .block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f2f3f7;

    .block-header-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .drag-handle {
      cursor: move;
      color: #909399;
      display: flex;
      align-items: center;

      :deep(svg) {
        width: 16px;
        height: 16px;
      }
    }

    .block-type {
      font-size: 14px;
      font-weight: 500;
      color: #b3b5c3;
    }

    .block-actions {
      display: flex;
      gap: 12px;

      :deep(svg) {
        cursor: pointer;
        color: #b3b5c3;
        font-size: 14px;
      }
    }
  }

  :deep(.arco-textarea) {
    border: none;
    border-radius: 0;

    &:focus {
      box-shadow: none;
    }
  }
}

.add-block-button {
  display: flex;
  justify-content: center;
  margin-top: 8px;

  :deep(.arco-btn) {
    color: #b3b5c3;
    border-color: #c9c9d6;

    &:hover {
      color: #5147ff;
      border-color: #5147ff;
    }
  }
}

.preview-actions {
  display: flex;
  justify-content: flex-end;

  .run-button {
    background-color: #5147ff;
    border: none;

    &:hover {
      background-color: #3b34c5;
    }
  }
}

:deep(.arco-form-item-label-required) {
  &::before {
    color: #f85149;
  }
}
</style>
