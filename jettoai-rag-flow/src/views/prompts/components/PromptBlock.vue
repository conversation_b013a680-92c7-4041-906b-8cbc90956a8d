<template>
  <div class="block-item-wrapper">
    <div class="block-item">
      <div class="block-item-header">
        <a-space />
        <a-space class="cursor-pointer">
          <icon-up-circle />
          <icon-delete />
        </a-space>
      </div>
      <div class="block-item-container">22</div>
    </div>
    <a-button class="w-full">
      <icon-plus />
      添加提示词块
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { ExtendedPromptDTO } from '@/apis/prompts/type'

defineOptions({
  name: 'PromptBlock'
})

const promptBlockData = ref<ExtendedPromptDTO[]>([])
</script>

<style scoped lang="scss">
.block-item-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.block-item {
  border: 1px solid #c9c9d6;
  border-radius: 8px;
  &-header {
    border-bottom: 1px solid #c9c9d6;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: var(--color-text-3);
  }
}
</style>
