<template>
  <AiPageLayout>
    <a-tabs v-model:activeKey="activeKey">
      <template #extra>
        <a-space>
          <a-input-search
            v-model="queryForm.name"
            :style="{ width: '220px' }"
            placeholder="请输入关键字搜索Prompt..."
            allow-clear
            @search="search"
            @press-enter="search"
          />
          <a-button type="primary" @click="onAdd">创建提示词</a-button>
        </a-space>
      </template>
      <a-tab-pane key="1" title="全部" />
      <a-tab-pane key="2" title="场景提示词" />
      <a-tab-pane key="3" title="提示词块" />
    </a-tabs>

    <AiTable
      row-key="id"
      :data="dataList || []"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #type="{ record }">
        <span>{{ record?.type === 1 ? '系统提示词' : '用户提示词' }}</span>
      </template>
      <template #tags="{ record }">
        <div class="tag-list">
          <a-tag v-for="(tag, index) in record.tags?.split(',')" :key="index" color="arcoblue" bordered>
            {{ tag }}
          </a-tag>
        </div>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link @click="onDetail(record)">测试</a-link>
          <a-link @click="onUpdate(record)">修改</a-link>
          <a-link @click="onPublish(record)">复制</a-link>
          <a-link @click="onDelete(record)">删除</a-link>
        </a-space>
      </template>
    </AiTable>
  </AiPageLayout>
</template>

<script setup lang="ts">
import type { TableInstance } from '@arco-design/web-vue'
import { deleteUsingDelete, pageUsingPost, updateUsingPut } from '@/apis/prompts'
import { useTable } from '@/hooks'
import { Prompt, RIPagePrompt } from '@/apis/prompts/type'

defineOptions({ name: 'PromptsManager' })

const activeKey = ref('1')
const router = useRouter()
const queryForm = reactive<{
  name?: string
  type?: number
}>({})

watch(activeKey, (val) => {
  if (val === '1') {
    queryForm.type = undefined
  } else if (val === '2') {
    queryForm.type = 1
  } else if (val === '3') {
    queryForm.type = 2
  }
  search()
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable<RIPagePrompt>((page) => pageUsingPost({ ...page, model: queryForm }), {
  immediate: true,
  dataKey: 'records'
})

const columns: TableInstance['columns'] = [
  { title: 'Prompt Key', dataIndex: 'promptKey', ellipsis: true, tooltip: true, minWidth: 120 },
  { title: '名称', dataIndex: 'name', ellipsis: true, tooltip: true, minWidth: 120 },
  { title: '类型', dataIndex: 'type', slotName: 'type', ellipsis: true, width: 100 },
  { title: '标签', dataIndex: 'tags', slotName: 'tags', minWidth: 120 },
  { title: '版本', dataIndex: 'version', width: 80 },
  { title: '描述', dataIndex: 'description', ellipsis: true, tooltip: true },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true, width: 150 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 200,
    align: 'left'
  }
]

// 删除
const onDelete = (record: Prompt) => {
  return handleDelete(() => deleteUsingDelete([record.id!]), {
    content: `是否确定删除提示词「${record.name}」？`,
    showModal: true
  })
}

// 发布
const onPublish = async (record: Prompt) => {
  try {
    await updateUsingPut({
      id: record.id,
      status: 2
    })
    search()
  } catch (error) {
    console.error('发布失败', error)
  }
}

// 新增
const onAdd = () => {
  router.push('/prompts/create')
}

const onDetail = (record) => {}

const onUpdate = (record) => {}
</script>

<style scoped lang="scss">
:deep(.arco-tabs .arco-tabs-nav-type-card-gutter .arco-tabs-tab-active) {
  box-shadow:
    inset 0 2px 0 rgb(var(--primary-6)),
    inset -1px 0 0 var(--color-border-2),
    inset 1px 0 0 var(--color-border-2);
  position: relative;
}

:deep(.arco-tabs-nav-type-card-gutter .arco-tabs-tab) {
  border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
}

:deep(.arco-tabs-type-card-gutter > .arco-tabs-content) {
  border: none;
}

:deep(.arco-tabs-nav::before) {
  left: -20px;
  right: -20px;
}

:deep(.arco-tabs) {
  overflow: visible;
}

:deep(.arco-tabs-nav) {
  overflow: visible;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
