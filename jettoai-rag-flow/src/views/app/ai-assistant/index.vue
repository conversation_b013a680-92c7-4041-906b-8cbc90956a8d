<template>
  <div class="ai-assistant-page h-full flex flex-col">
    <!-- 头部 -->
    <div class="header bg-white border-b border-gray-200 p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <icon-robot class="text-2xl text-blue-500" />
          <div>
            <h1 class="text-xl font-semibold text-gray-900">AI 助手</h1>
            <p class="text-sm text-gray-500">基于 Chatflow 的智能对话助手</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <a-button v-if="!conversationStarted" type="primary" @click="startNewConversation">
            <icon-plus class="mr-1" />
            新建对话
          </a-button>
          <a-button v-if="conversationStarted" @click="showSettings = true">
            <icon-settings class="mr-1" />
            设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 侧边栏 - 对话历史 -->
      <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">对话历史</h2>
        </div>
        <div class="flex-1 overflow-y-auto p-4">
          <div v-if="conversationHistory.length === 0" class="text-center text-gray-500 mt-8">
            <icon-message class="text-4xl mb-2" />
            <p>暂无对话历史</p>
          </div>
          <div v-else class="space-y-2">
            <div
              v-for="conversation in conversationHistory"
              :key="conversation.id"
              class="p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
              :class="{ 'bg-blue-50 border-blue-300': currentConversationId === conversation.id }"
              @click="loadConversation(conversation.id)"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-medium text-gray-900 truncate">{{ conversation.title }}</h3>
                  <p class="text-xs text-gray-500 mt-1">{{ formatTime(conversation.created_at) }}</p>
                </div>
                <a-dropdown>
                  <a-button type="text" size="mini">
                    <icon-more />
                  </a-button>
                  <template #content>
                    <a-doption @click="renameConversation(conversation)">
                      <icon-edit class="mr-2" />
                      重命名
                    </a-doption>
                    <a-doption @click="deleteConversation(conversation.id)">
                      <icon-delete class="mr-2" />
                      删除
                    </a-doption>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主聊天区域 -->
      <div class="flex-1 flex flex-col">
        <div v-if="!conversationStarted" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <icon-robot class="text-6xl text-gray-300 mb-4" />
            <h2 class="text-2xl font-semibold text-gray-700 mb-2">欢迎使用 AI 助手</h2>
            <p class="text-gray-500 mb-6">选择一个应用开始对话，或创建新的对话</p>
            <a-button type="primary" size="large" @click="startNewConversation">
              <icon-plus class="mr-2" />
              开始新对话
            </a-button>
          </div>
        </div>

        <div v-else class="flex-1 flex flex-col">
          <!-- 应用选择器 -->
          <div class="bg-white border-b border-gray-200 p-4">
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <a-select
                  v-model="selectedAppId"
                  placeholder="选择一个 Chatflow 应用"
                  class="w-full"
                  @change="handleAppChange"
                >
                  <a-option v-for="app in chatflowApps" :key="app.id" :value="app.id" :label="app.name">
                    <div class="flex items-center">
                      <icon-apps class="mr-2" />
                      {{ app.name }}
                    </div>
                  </a-option>
                </a-select>
              </div>
              <a-button v-if="selectedAppId" @click="showAppDetail = true">
                <icon-info-circle class="mr-1" />
                应用详情
              </a-button>
            </div>
          </div>

          <!-- 聊天区域 -->
          <div v-if="selectedAppId" class="flex-1">
            <ChatflowRoom
              :app-id="selectedAppId"
              :variables="currentAppVariables"
              :is-running="isExecuting"
              :form-data="formData"
              @start-execution="handleStartExecution"
              @stop-execution="handleStopExecution"
              @show-detail="handleShowDetail"
            />
          </div>

          <div v-else class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <icon-apps class="text-4xl text-gray-300 mb-4" />
              <p class="text-gray-500">请选择一个 Chatflow 应用开始对话</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置弹窗 -->
    <a-modal
      v-model:visible="showSettings"
      title="对话设置"
      :width="600"
      @ok="saveSettings"
      @cancel="showSettings = false"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">对话标题</label>
          <a-input v-model="conversationTitle" placeholder="输入对话标题" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">应用变量</label>
          <div v-if="currentAppVariables.length > 0" class="space-y-3">
            <div v-for="variable in currentAppVariables" :key="variable.variable" class="flex items-center space-x-3">
              <label class="w-24 text-sm text-gray-600">{{ variable.label || variable.variable }}</label>
              <a-input
                v-model="formData[variable.variable]"
                :placeholder="variable.placeholder || `请输入${variable.label || variable.variable}`"
                :required="variable.required"
                class="flex-1"
              />
            </div>
          </div>
          <div v-else class="text-gray-500 text-sm">当前应用无需配置变量</div>
        </div>
      </div>
    </a-modal>

    <!-- 应用详情弹窗 -->
    <a-modal v-model:visible="showAppDetail" title="应用详情" :width="800" :footer="false">
      <div v-if="selectedApp" class="space-y-4">
        <div class="flex items-center space-x-3">
          <icon-apps class="text-2xl text-blue-500" />
          <div>
            <h3 class="text-lg font-semibold">{{ selectedApp.name }}</h3>
            <p class="text-sm text-gray-500">{{ selectedApp.description || '暂无描述' }}</p>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">应用类型</label>
            <p class="text-sm text-gray-900">
              {{ selectedApp.mode === 'advanced-chat' ? 'Chatflow' : selectedApp.mode }}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
            <p class="text-sm text-gray-900">{{ formatTime(selectedApp.created_at) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">更新时间</label>
            <p class="text-sm text-gray-900">{{ formatTime(selectedApp.updated_at) }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <a-tag :color="selectedApp.enable_site ? 'green' : 'red'">
              {{ selectedApp.enable_site ? '已启用' : '未启用' }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 重命名对话弹窗 -->
    <a-modal v-model:visible="showRenameModal" title="重命名对话" @ok="confirmRename" @cancel="showRenameModal = false">
      <a-input v-model="newConversationTitle" placeholder="输入新的对话标题" @keyup.enter="confirmRename" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import ChatflowRoom from '../workflow/run/chatflow-room.vue'
import { getAppList } from '@/apis/apps'
import { AppMode } from '@/apis/apps/type'

// 响应式数据
const conversationStarted = ref(false)
const selectedAppId = ref('')
const chatflowApps = ref([])
const currentAppVariables = ref([])
const formData = ref({})
const isExecuting = ref(false)
const showSettings = ref(false)
const showAppDetail = ref(false)
const showRenameModal = ref(false)
const conversationTitle = ref('')
const newConversationTitle = ref('')
const currentConversationId = ref('')
const conversationHistory = ref([])

// 计算属性
const selectedApp = computed(() => {
  return chatflowApps.value.find((app) => app.id === selectedAppId.value)
})

// 生命周期
onMounted(async () => {
  await loadChatflowApps()
  loadConversationHistory()
})

// 方法
const loadChatflowApps = async () => {
  try {
    const response = await getAppList()
    chatflowApps.value = response.data.filter((app) => app.mode === AppMode.ADVANCED_CHAT)
  } catch (error) {
    console.error('加载应用失败:', error)
    Message.error('加载应用失败')
  }
}

const startNewConversation = () => {
  conversationStarted.value = true
  currentConversationId.value = ''
  conversationTitle.value = `对话 ${new Date().toLocaleString()}`
}

const handleAppChange = (appId: string) => {
  const app = chatflowApps.value.find((a) => a.id === appId)
  if (app) {
    // 这里需要获取应用的变量配置
    // currentAppVariables.value = app.variables || []
    currentAppVariables.value = []
    formData.value = {}
  }
}

const handleStartExecution = () => {
  isExecuting.value = true
}

const handleStopExecution = () => {
  isExecuting.value = false
}

const handleShowDetail = (message: any) => {
  // 跳转到详情页面或显示详情弹窗
  console.log('显示详情:', message)
}

const saveSettings = () => {
  showSettings.value = false
  Message.success('设置已保存')
}

const loadConversation = (conversationId: string) => {
  currentConversationId.value = conversationId
  // 加载对话内容
}

const loadConversationHistory = () => {
  // 加载对话历史
  conversationHistory.value = []
}

const renameConversation = (conversation: any) => {
  newConversationTitle.value = conversation.title
  showRenameModal.value = true
}

const confirmRename = () => {
  // 重命名对话
  showRenameModal.value = false
  Message.success('重命名成功')
}

const deleteConversation = (conversationId: string) => {
  // 删除对话
  Message.success('删除成功')
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleString()
}
</script>

<style scoped lang="scss">
.ai-assistant-page {
  background-color: #f5f5f5;
}

.header {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
