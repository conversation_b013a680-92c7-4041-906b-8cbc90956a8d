<template>
  <div class="test-chatflow-page p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">Chatflow 功能测试</h1>
      <p class="text-gray-600">测试 Chatflow 运行功能的各项特性</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基础功能测试 -->
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h2 class="text-lg font-semibold mb-4">基础功能测试</h2>
        <div class="space-y-3">
          <a-button type="primary" @click="testBasicChat">
            <icon-message class="mr-2" />
            测试基础对话
          </a-button>
          <a-button @click="testStopExecution">
            <icon-record-stop class="mr-2" />
            测试停止执行
          </a-button>
          <a-button @click="testRestartConversation">
            <icon-refresh class="mr-2" />
            测试重新开始
          </a-button>
          <a-button @click="testCopyMessage">
            <icon-copy class="mr-2" />
            测试复制消息
          </a-button>
        </div>
      </div>

      <!-- 表单验证测试 -->
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h2 class="text-lg font-semibold mb-4">表单验证测试</h2>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">必填字段</label>
            <a-input v-model="testFormData.required_field" placeholder="这是必填字段" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">可选字段</label>
            <a-input v-model="testFormData.optional_field" placeholder="这是可选字段" />
          </div>
          <a-button type="primary" @click="testFormValidation">
            <icon-check class="mr-2" />
            测试表单验证
          </a-button>
        </div>
      </div>

      <!-- 节点状态测试 -->
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h2 class="text-lg font-semibold mb-4">节点状态测试</h2>
        <div class="space-y-3">
          <a-button @click="simulateNodeExecution">
            <icon-play-arrow class="mr-2" />
            模拟节点执行
          </a-button>
          <a-button @click="simulateNodeError">
            <icon-exclamation-circle class="mr-2" />
            模拟节点错误
          </a-button>
          <a-button @click="clearNodeStatus">
            <icon-delete class="mr-2" />
            清除节点状态
          </a-button>
        </div>
      </div>

      <!-- 实际 Chatflow 组件 -->
      <div class="bg-white rounded-lg border border-gray-200 p-4 lg:col-span-2">
        <h2 class="text-lg font-semibold mb-4">Chatflow 组件</h2>
        <div class="h-96 border border-gray-300 rounded-lg">
          <ChatflowRoom
            :app-id="testAppId"
            :variables="testVariables"
            :is-running="isRunning"
            :form-data="testFormData"
            @start-execution="handleStartExecution"
            @stop-execution="handleStopExecution"
            @show-detail="handleShowDetail"
          />
        </div>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div v-if="testResults.length > 0" class="mt-6 bg-gray-50 rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">测试结果</h3>
      <div class="space-y-2">
        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="flex items-center space-x-2 text-sm"
        >
          <div
            class="w-2 h-2 rounded-full"
            :class="{
              'bg-green-500': result.status === 'success',
              'bg-red-500': result.status === 'error',
              'bg-blue-500': result.status === 'info'
            }"
          />
          <span class="text-gray-600">{{ result.timestamp }}</span>
          <span>{{ result.message }}</span>
        </div>
      </div>
      <a-button size="small" class="mt-3" @click="clearTestResults">
        清除结果
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import ChatflowRoom from './chatflow-room.vue'

// 测试数据
const testAppId = ref('test-app-123')
const isRunning = ref(false)
const testFormData = ref({
  required_field: '',
  optional_field: ''
})

const testVariables = ref([
  {
    variable: 'required_field',
    label: '必填字段',
    type: 'text-input',
    required: true,
    placeholder: '请输入必填字段'
  },
  {
    variable: 'optional_field',
    label: '可选字段',
    type: 'text-input',
    required: false,
    placeholder: '请输入可选字段'
  },
  {
    variable: 'query',
    label: '查询内容',
    type: 'paragraph',
    required: false,
    placeholder: '请输入查询内容'
  }
])

const testResults = ref<Array<{
  timestamp: string
  message: string
  status: 'success' | 'error' | 'info'
}>>([])

// 添加测试结果
const addTestResult = (message: string, status: 'success' | 'error' | 'info' = 'info') => {
  testResults.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    status
  })
}

// 测试方法
const testBasicChat = () => {
  addTestResult('开始测试基础对话功能', 'info')
  // 这里可以添加具体的测试逻辑
  setTimeout(() => {
    addTestResult('基础对话功能测试完成', 'success')
  }, 1000)
}

const testStopExecution = () => {
  addTestResult('测试停止执行功能', 'info')
  isRunning.value = true
  setTimeout(() => {
    isRunning.value = false
    addTestResult('停止执行功能测试完成', 'success')
  }, 2000)
}

const testRestartConversation = () => {
  addTestResult('测试重新开始对话功能', 'info')
  // 重置表单数据
  testFormData.value = {
    required_field: '',
    optional_field: ''
  }
  addTestResult('重新开始对话功能测试完成', 'success')
}

const testCopyMessage = async () => {
  addTestResult('测试复制消息功能', 'info')
  try {
    await navigator.clipboard.writeText('测试复制的消息内容')
    addTestResult('复制消息功能测试完成', 'success')
    Message.success('复制成功')
  } catch (error) {
    addTestResult('复制消息功能测试失败', 'error')
    Message.error('复制失败')
  }
}

const testFormValidation = () => {
  addTestResult('测试表单验证功能', 'info')
  
  if (!testFormData.value.required_field.trim()) {
    addTestResult('表单验证测试：必填字段为空，验证通过', 'success')
    Message.error('请填写必填字段: 必填字段')
  } else {
    addTestResult('表单验证测试：所有必填字段已填写', 'success')
    Message.success('表单验证通过')
  }
}

const simulateNodeExecution = () => {
  addTestResult('模拟节点执行状态', 'info')
  isRunning.value = true
  
  setTimeout(() => {
    isRunning.value = false
    addTestResult('节点执行模拟完成', 'success')
  }, 3000)
}

const simulateNodeError = () => {
  addTestResult('模拟节点错误状态', 'info')
  addTestResult('节点执行出现错误：模拟错误', 'error')
}

const clearNodeStatus = () => {
  addTestResult('清除节点状态', 'info')
  isRunning.value = false
  addTestResult('节点状态已清除', 'success')
}

const clearTestResults = () => {
  testResults.value = []
}

// 事件处理
const handleStartExecution = () => {
  isRunning.value = true
  addTestResult('Chatflow 开始执行', 'info')
}

const handleStopExecution = () => {
  isRunning.value = false
  addTestResult('Chatflow 停止执行', 'info')
}

const handleShowDetail = (message: any) => {
  addTestResult(`显示详情: ${JSON.stringify(message)}`, 'info')
}
</script>

<style scoped lang="scss">
.test-chatflow-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}
</style>
