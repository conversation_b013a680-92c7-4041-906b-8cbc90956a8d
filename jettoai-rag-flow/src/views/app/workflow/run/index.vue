<template>
  <a-drawer
    popup-container="#parentNode"
    :visible="visible"
    :footer="false"
    :mask="false"
    class="run-panel-drawer"
    :width="400"
    unmountOnClose
    :drawerStyle="{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }"
    :body-style="{ padding: 0 }"
    @cancel="handleCancel"
  >
    <template #title>调试</template>
    <div class="h-[100%]">
      <a-tabs v-model:active-key="tabsKey" justify>
        <a-tab-pane v-if="variables.length > 0" key="inputs" title="输入">
          <InputsPanel
            ref="inputsPanel"
            :isAdvancedChat="isAdvancedChat"
            :variables="variables"
            @formDataChange="handleFormDataChange"
            @switchTab="switchTab"
          />
        </a-tab-pane>
        <!-- Chatflow 专用的聊天界面 -->
        <a-tab-pane v-if="isAdvancedChat" key="chat" title="对话">
          <ChatflowRoom
            ref="chatflowRoom"
            :app-id="appId"
            :variables="variables"
            :is-running="isRunning"
            :show-variable-form="false"
            :formData="formData"
            @start-execution="handleStartExecution"
            @stop-execution="stopExecution"
            @show-detail="handleShowDetail"
          />
        </a-tab-pane>
        <a-tab-pane v-if="!isAdvancedChat" key="result" :disabled="!workflowRunningData" title="结果">
          <ResultText
            :isRunning="workflowRunningData?.result?.status === 'running' || !workflowRunningData?.result"
            :outputs="workflowRunningData?.resultText"
            :allFiles="workflowRunningData?.result?.files"
            :error="workflowRunningData?.result?.error"
          />
        </a-tab-pane>
        <a-tab-pane key="detail" :disabled="!workflowRunningData" title="详情">
          <ResultPanel
            :inputs="workflowRunningData?.result?.inputs"
            :outputs="workflowRunningData?.result?.outputs"
            :status="workflowRunningData?.result?.status || ''"
            :error="workflowRunningData?.result?.error"
            :elapsed_time="workflowRunningData?.result?.elapsed_time"
            :total_tokens="workflowRunningData?.result?.total_tokens"
            :created_at="workflowRunningData?.result?.created_at"
            :created_by="(workflowRunningData?.result?.created_by as any)?.name"
            :steps="workflowRunningData?.result?.total_steps"
            :exceptionCounts="workflowRunningData?.result?.exceptions_count"
          />
        </a-tab-pane>
        <a-tab-pane key="tracing" :disabled="!workflowRunningData" title="追踪">
          <TracingPanel :list="workflowRunningData?.tracing || []" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import InputsPanel from './inputs-panel.vue'
import ResultText from './result-text.vue'
import ResultPanel from './result-panel.vue'
import TracingPanel from './tracing-panel.vue'
import ChatflowRoom from './chatflow-room.vue'

import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { AppMode } from '@/apis/apps/type'

defineOptions({ name: 'RunPanel' })
const emits = defineEmits(['hideRunPanel'])
const inputsPanel = ref()
const chatflowRoom = ref()
const { nodes } = useVueFlow()
const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)

// 获取开始节点配置的变量
const variables = ref([])
const tabsKey = ref<string>('inputs')
const visible = ref(false)
const isRunning = ref(false)
const stopLoading = ref(false)
const appId = ref('')

const { workflowRunningData } = storeToRefs(useWorkflowStore())

// 判断是否为 advanced-chat 模式
const isAdvancedChat = computed(() => {
  return currentAppInfo.value?.mode === AppMode.ADVANCED_CHAT
})

onMounted(() => {
  variables.value = nodes.value.find((e) => e.type === 'start')?.data?.variables || []
  appId.value = currentAppInfo.value?.id || ''

  // 如果是 chatflow 模式，默认显示对话标签
  // if (isAdvancedChat.value) {
  //   tabsKey.value = 'chat'
  // }
})

const handleCancel = () => {
  visible.value = false
  emits('hideRunPanel')
}

const switchTab = (v: string) => {
  tabsKey.value = v
}

const handleStartExecution = () => {
  isRunning.value = true
}

const stopExecution = async () => {
  stopLoading.value = true
  try {
    if (chatflowRoom.value) {
      await chatflowRoom.value.stopExecution()
    }
    isRunning.value = false
  } catch (error) {
    console.error('停止执行失败:', error)
  } finally {
    stopLoading.value = false
  }
}
const formData = ref({})
const handleFormDataChange = (e) => {
  formData.value = e
}

const handleShowDetail = (message: any) => {
  // 切换到详情tab页面
  tabsKey.value = 'result'
  console.log('显示详情:', message)
}
defineExpose({
  visible,
  isAdvancedChat,
  handleStartExecution,
  stopExecution
})
</script>
<style scoped lang="scss">
.run-panel-drawer {
  left: auto;
  right: 5px;
  top: 10px;
  bottom: 10px;

  .arco-drawer-header {
    height: 46px;
    border-bottom: 0;
    padding: 0 16px;
  }
}
:deep(.arco-tabs-content) {
  padding: 16px;
}
:deep(.arco-tabs-pane) {
  height: 100%;
  overflow-y: auto;
}
</style>
