<template>
  <div>
    <FormItem ref="runOnceRef" v-model:inputs="form" :promptConfig="variables!" />
    <a-button type="primary" class="w-full" @click="run">{{ !isAdvancedChat ? '开始运行' : '开始对话' }}</a-button>
  </div>
</template>

<script setup lang="ts">
import type { RunInputVariables } from './type'
import { handleRun } from './run'
import { Message } from '@arco-design/web-vue'

import FormItem from './component/FormItem.vue'

defineOptions({
  name: 'InputsPanel'
})

const props = withDefaults(
  defineProps<{
    variables?: RunInputVariables[]
    isAdvancedChat?: boolean
  }>(),
  {
    variables: () => [],
    isAdvancedChat: false
  }
)
const emits = defineEmits(['switchTab', 'formDataChange'])

const route = useRoute()

var form = reactive<Record<string, any>>({})

const validateVariables = () => {
  for (const variable of props.variables) {
    console.log(form[variable.variable])
    if (variable.required && !form[variable.variable]) {
      Message.error(`请填写必填字段: ${variable.label}`)
      return false
    }
  }
  return true
}
onMounted(() => {
  if (props.variables.length) {
    form = props.variables.reduce(
      (acc, variable) => {
        acc[variable.variable] = ''
        return acc
      },
      {} as Record<string, any>
    )
  }
})

const onInputChange = () => {
  console.log(form)
}
const run = () => {
  if (!validateVariables()) return
  emits('formDataChange', form)
  if (props.isAdvancedChat) {
    emits('switchTab', 'chat')
  } else {
    emits('switchTab', 'result')
    handleRun(route.params.appId as string, { inputs: form, files: [] })
  }
}
</script>

<style scoped lang="scss"></style>
