<template>
  <div class="chatflow-room relative">
    <!-- 聊天消息区域 -->
    <div ref="messagesContainer" class="messages-container">
      <div v-if="messages.length === 0" class="empty-state">
        <icon-robot class="text-6xl mb-4 text-gray-300" />
        <div class="empty-text">开始对话来调试您的聊天机器人</div>
      </div>

      <div v-for="(message, index) in messages" :key="index" class="message-item">
        <!-- 用户消息 -->
        <div v-if="message.role === 'user'" class="flex justify-end mb-4">
          <div class="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
            {{ message.content }}
          </div>
        </div>

        <!-- 助手消息 -->
        <div v-else-if="message.role === 'assistant'" class="flex justify-start mb-4">
          <div class="bg-white text-gray-800 rounded-lg py-2 max-w-xs lg:max-w-md overflow-hidden relative group">
            <!-- <div v-if="message.isStreaming" class="flex items-center">
              <div class="typing-indicator">
                <span />
                <span />
                <span />
              </div>
            </div> -->
            <div>
              <AiMarkdown :content="message.content" />
              <!-- <div v-html="formatMessage(message.content)" /> -->
            </div>
            <!-- 消息操作按钮 -->
            <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
              <a-button size="mini" type="text" @click="copyMessageContent(message)">
                <icon-copy class="text-gray-500 hover:text-blue-500" />
              </a-button>
              <a-button size="mini" type="text" @click="showExecutionDetail(message)">
                <icon-info-circle class="text-gray-500 hover:text-blue-500" />
              </a-button>
            </div>
          </div>
        </div>

        <!-- 系统状态消息 -->
        <div v-else-if="message.role === 'system'" class="flex justify-center mb-4">
          <div class="bg-yellow-100 border border-yellow-300 rounded-lg px-4 py-2 text-sm">
            <div class="flex items-center space-x-2">
              <icon-loading v-if="message.status === 'running'" class="animate-spin" />
              <icon-check-circle v-else-if="message.status === 'success'" class="text-green-500" />
              <icon-exclamation-circle v-else-if="message.status === 'error'" class="text-red-500" />
              <span>{{ message.content }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 节点执行状态 -->
      <div v-if="currentNodeStatus" class="flex justify-center mb-4">
        <div class="bg-blue-100 border border-blue-300 rounded-lg px-4 py-2 text-sm">
          <div class="flex items-center space-x-2">
            <icon-loading class="animate-spin text-blue-500" />
            <span>正在执行: {{ currentNodeStatus.title }}</span>
          </div>
        </div>
      </div>

      <!-- 节点执行列表 -->
      <div v-if="nodeStatuses.size > 0" class="mb-4 mx-4">
        <div class="bg-white rounded-lg border border-gray-200 p-3">
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-sm font-medium text-gray-700">节点执行状态</h3>
            <a-button size="mini" type="text" @click="toggleNodeList">
              <icon-down v-if="!showNodeList" />
              <icon-up v-else />
            </a-button>
          </div>
          <div v-if="showNodeList" class="space-y-2 max-h-32 overflow-y-auto">
            <div
              v-for="[nodeId, status] in nodeStatuses"
              :key="nodeId"
              class="flex items-center justify-between text-xs"
            >
              <div class="flex items-center space-x-2">
                <div
                  class="w-2 h-2 rounded-full"
                  :class="{
                    'bg-blue-500': status.status === 'running',
                    'bg-green-500': status.status === 'success',
                    'bg-red-500': status.status === 'error',
                    'bg-gray-400': status.status === 'stopped'
                  }"
                />
                <span class="text-gray-700">{{ status.title }}</span>
              </div>
              <div class="text-gray-500">
                <span v-if="status.elapsed_time">{{ status.elapsed_time.toFixed(2) }}s</span>
                <span v-else-if="status.status === 'running'">运行中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 控制按钮 -->
    <div class="m-auto mb-2 absolute bottom-[60px] left-[calc(50%-100px)] flex space-x-2">
      <a-button v-if="isExecuting" style="background-color: white" size="small" @click="stopExecution">
        <icon-record-stop class="mr-1" />
        停止响应
      </a-button>
      <a-button
        v-if="!isExecuting && messages.length > 0"
        style="background-color: white"
        size="small"
        @click="handleRestartConversation"
      >
        <icon-refresh class="mr-1" />
        重新开始
      </a-button>
    </div>
    <!-- 输入区域 -->
    <div class="p-3 w-[95%] bg-white m-auto mb-2 rounded-xl shadow-md">
      <!-- 普通聊天输入 -->
      <div class="flex space-x-2 items-center">
        <a-textarea
          v-model="inputMessage"
          auto-size
          placeholder="输入消息..."
          :disabled="isExecuting"
          class="flex-1 border-0"
          @keyup.enter="sendMessage"
        />
        <a-button
          type="primary"
          class="px-3"
          :disabled="!inputMessage.trim() || isExecuting"
          :loading="isExecuting"
          size="small"
          @click="sendMessage"
        >
          <icon-send />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { chatflowRunFetch, stopChatflowRun } from '@/apis/workflow/chatflow'
import { AppMode } from '@/apis/apps/type'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useChatflowRunEvent } from '../hooks/use-chatflow-run-event'
import { Message } from '@arco-design/web-vue'

interface Props {
  appId: string
  variables: any[]
  isRunning: boolean
  showVariableForm?: boolean
  formData?: Record<string, any>
}

const props = defineProps<Props>()
const emits = defineEmits(['start-execution', 'stop-execution', 'show-detail'])

const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)

// 使用 chatflow 运行事件处理器
const {
  runState,
  addMessage,
  handleWorkflowStarted,
  handleWorkflowFinished,
  handleWorkflowFailed,
  handleNodeStarted,
  handleNodeFinished,
  handleTextChunk,
  handleTextReplace,
  handleAgentLog,
  getCurrentNodeStatus,
  stopExecution: stopExecutionHandler,
  copyMessage,
  restartConversation
} = useChatflowRunEvent()

const inputMessage = ref('')
const messagesContainer = ref()
const abortController = ref<AbortController | null>(null)
const internalShowVariableForm = ref(true)
const showNodeList = ref(true)

// 控制是否显示变量表单
const showVariableForm = computed(() => {
  return props.showVariableForm !== false && props.variables.length > 0 && internalShowVariableForm.value
})

// 判断是否为 advanced-chat 模式
const isAdvancedChat = computed(() => {
  return currentAppInfo.value?.mode === AppMode.ADVANCED_CHAT
})

// 从运行状态中获取数据
const messages = computed(() => runState.value.messages)
const isExecuting = computed(() => runState.value.isRunning)
const currentNodeStatus = computed(() => getCurrentNodeStatus())
const nodeStatuses = computed(() => runState.value.nodeStatuses)
const formValue = computed(() => props.formData)

// 切换节点列表显示
const toggleNodeList = () => {
  showNodeList.value = !showNodeList.value
}

onMounted(() => {})

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 验证必填字段
const validateRequiredFields = () => {
  const requiredFields = props.variables.filter((v: any) => v.required)
  const missingFields = []

  for (const field of requiredFields) {
    const value = formValue.value[field.variable]
    if (!value || (typeof value === 'string' && !value.trim())) {
      missingFields.push(field.label || field.variable)
    }
  }

  return missingFields
}

const sendMessage = async (e?: KeyboardEvent | MouseEvent) => {
  if (e && 'shiftKey' in e && e.shiftKey) {
    return
  }
  if (!inputMessage.value.trim()) return

  // 验证必填字段
  const missingFields = validateRequiredFields()
  if (missingFields.length > 0) {
    Message.error(`请填写必填字段: ${missingFields.join(', ')}`)
    return
  }

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  addMessage({
    role: 'user',
    content: userMessage
  })

  scrollToBottom()
  emits('start-execution')
  try {
    // 构建输入参数
    const inputs = { ...formValue.value }

    // 查找查询变量
    const queryVar = props.variables.find(
      (v: any) =>
        v.variable === 'query' || v.variable === 'input' || v.variable === 'question' || v.variable === 'message'
    )

    if (queryVar) {
      inputs[queryVar.variable] = userMessage
    } else if (props.variables.length > 0) {
      // 如果没有明确的查询变量，使用第一个文本变量
      const firstTextVar = props.variables.find((v: any) => v.type === 'text-input' || v.type === 'paragraph')
      if (firstTextVar) {
        inputs[firstTextVar.variable] = userMessage
      }
    }

    await executeWorkflow(inputs, userMessage)
  } catch (error) {
    console.error('消息发送失败:', error)
    handleWorkflowFailed('消息发送失败')
  }
}

// 复制消息内容
const copyMessageContent = async (message: any) => {
  try {
    const success = await copyMessage(message)
    if (success) {
      Message.success('复制成功')
    } else {
      Message.error('复制失败')
    }
  } catch (error) {
    Message.error('复制失败')
  }
}

// 显示执行详情
const showExecutionDetail = (message: any) => {
  // 跳转到详情tab页面
  emits('show-detail', message)
}

// 重新开始对话
const handleRestartConversation = () => {
  restartConversation()
  Message.success('对话已重新开始')
}

const executeWorkflow = async (inputs: Record<string, any>, query?: string) => {
  try {
    // 构建正确的请求参数格式
    const requestBody = {
      files: [],
      inputs,
      query: query || inputs.query || '',
      conversation_id: ''
    }
    if (isAdvancedChat.value) {
      abortController.value = chatflowRunFetch(
        props.appId,
        {
          body: requestBody
        },
        {
          onData: (data) => {
            const text = {
              task_id: runState.value.taskId || '',
              workflow_run_id: runState.value.workflowRunId || '',
              event: 'text_chunk',
              data: {
                text: data as string
              }
            }
            handleTextChunk(text)
          },
          onWorkflowStarted: handleWorkflowStarted,
          onWorkflowFinished: (data) => {
            handleWorkflowFinished(data)
            emits('stop-execution')
            // 添加执行完成的反馈消息
            if (data.data?.status === 'succeeded') {
              addMessage({
                role: 'system',
                content: '✅ 工作流执行成功',
                status: 'success'
              })
            } else {
              addMessage({
                role: 'system',
                content: '❌ 工作流执行失败',
                status: 'error'
              })
            }

            scrollToBottom()
          },
          onNodeStarted: (data) => {
            handleNodeStarted(data)
            scrollToBottom()
          },
          onNodeFinished: handleNodeFinished,
          onMessageEnd: () => {},
          onTextChunk: (data) => {
            handleTextChunk(data)

            scrollToBottom()
          },
          onTextReplace: (data) => {
            handleTextReplace(data)
            scrollToBottom()
          },
          onAgentLog: handleAgentLog,
          onError: (error) => {
            handleWorkflowFailed(error)
            emits('stop-execution')
          }
        }
      )
    }
  } catch (error) {
    console.error('执行工作流时出错:', error)
    handleWorkflowFailed('执行失败')
  }
}

const stopExecution = async () => {
  // 防止重复调用
  if (!isExecuting.value) {
    return
  }

  try {
    // 如果有taskId，调用停止API
    if (runState.value.taskId) {
      await stopChatflowRun(props.appId, runState.value.taskId)
    }
  } catch (error) {
    console.error('调用停止API失败:', error)
  }

  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }

  // 使用事件处理器的停止方法
  stopExecutionHandler()

  emits('stop-execution')
  scrollToBottom()
}

defineExpose({
  stopExecution
})
</script>

<style scoped lang="scss">
.chatflow-room {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fd;
  .variable-form {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;

    .form-header {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 12px;
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #9ca3af;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 8px;
      }

      .empty-text {
        font-size: 14px;
      }
    }

    .message-item {
      margin-bottom: 16px;
    }
  }

  .chat-input {
    padding: 16px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .typing-indicator {
    display: flex;
    align-items: center;

    span {
      height: 4px;
      width: 4px;
      background-color: #9ca3af;
      border-radius: 50%;
      display: inline-block;
      margin-right: 2px;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
