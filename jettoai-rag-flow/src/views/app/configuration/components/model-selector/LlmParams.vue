<template>
  <div>
    <div v-for="(paramsItem, index) in paramsList" :key="index" class="retry-item">

      <div class="retry-label">
        <a-switch
          v-model="paramsItem.isChecked"
          :size="'small'"
          type="round"
          @change="handleChangeLlmParams(paramsItem, index, $event)"
        />
        {{ paramsItem?.label?.zh_Hans }}

        <a-tooltip :content="paramsItem.help?.zh_Hans">
          <icon-info-circle />
        </a-tooltip>
      </div>
      <div class="retry-value">
        <template v-if="paramsItem.type === 'int' || paramsItem.type === 'float'">
          <a-input-number
            v-model="paramsItem.default"
            :precision="paramsItem.precision"
            :step="paramsItem.max ? (Math.floor(paramsItem.max / 10) > 1 ? 1 : 0.1) : 1"
            :min="paramsItem.min ? paramsItem.min : -Infinity"
            :max="paramsItem.max ? paramsItem.max : Infinity"
            placeholder="请输入"
            style="width: 100px"
            @change="handleChangeValue(paramsItem)"
          />
        </template>

        <template v-if="paramsItem.type === 'string'">
          <a-select v-model="paramsItem.default" style="width: 100px">
            <a-option v-for="item in paramsItem.options" :key="item" :value="item">{{ item }}</a-option>
          </a-select>
        </template>
      </div>
    </div>
    <!--<div v-for="(paramsItem, index) in paramsList" :key="index" class="retry-item">
      <div class="retry-label">
        <a-switch
          :model-value="Object.keys(completion_params).some((v) => v === paramsItem.name)"
          :size="'small'"
          type="round"
          @change="handleChangeLlmParams(paramsItem, index, $event)"
        />
        {{ paramsItem?.label?.zh_Hans }}

        <a-tooltip :content="paramsItem.help?.zh_Hans">
          <icon-info-circle />
        </a-tooltip>
      </div>
      <div class="retry-value">
        <template v-if="Object.keys(completion_params).some((v) => v === paramsItem.name)">
          <template v-if="paramsItem.type === 'int' || paramsItem.type === 'float'">
            <a-input-number
              v-model="completionParams[paramsItem.name]"
              :precision="paramsItem.precision"
              :step="paramsItem.max ? (Math.floor(paramsItem.max / 10) > 1 ? 1 : 0.1) : 1"
              :min="paramsItem.min ? paramsItem.min : -Infinity"
              :max="paramsItem.max ? paramsItem.max : Infinity"
              placeholder="请输入"
              style="width: 100px"
            />
          </template>

          <template v-if="paramsItem.type === 'string'">
            <a-select v-model="completionParams[paramsItem.name]" style="width: 100px">
              <a-option v-for="item in paramsItem.options" :key="item" :value="item">{{ item }}</a-option>
            </a-select>
          </template>
        </template>
        <template v-else>
          <template v-if="paramsItem.type === 'int' || paramsItem.type === 'float'">
            <a-input-number
              v-model="paramsItem.default"
              :precision="paramsItem.precision"
              :step="paramsItem.max ? (Math.floor(paramsItem.max / 10) > 1 ? 1 : 0.1) : 1"
              :min="paramsItem.min ? paramsItem.min : -Infinity"
              :max="paramsItem.max ? paramsItem.max : Infinity"
              placeholder="请输入"
              style="width: 100px"
            />
          </template>

          <template v-if="paramsItem.type === 'string'">
            <a-select v-model="paramsItem.default" style="width: 100px">
              <a-option v-for="item in paramsItem.options" :key="item" :value="item">{{ item }}</a-option>
            </a-select>
          </template>
        </template>
      </div>
    </div>-->
  </div>
</template>

<script setup lang="ts">
import { useProviderStore } from '@/stores/modules/workflow/provider'

/**
 * paramsList: 自定义表单form的list。
 * completion_params：默认选中checkbox的内容。eg：
 * {stop: [], temperature: 0.3, top_p: 0.2}
 */
const props = withDefaults(
  defineProps<{
    paramsList?: any[]
    customForm?: any[]
    completion_params?: Object
  }>(),
  {
    paramsList: () => [],
    customForm: () => [],
    completion_params: () => ({})
  }
)
console.log('llm-props：', props)
const completionParams = ref(props.completion_params)
const providerStore = useProviderStore()
// const paramsList = ref(providerStore.provider.paramsList)
const handleChangeLlmParams = (item, index, event) => {
  if (event) {
    // props.nodeInfo.model.completion_params[item.name] = item.default
  } else {
    // delete props.nodeInfo.model.completion_params[item.name]
  }
}
const emits = defineEmits(['updateParamsList'])
const handleChangeValue = (item) => {
  if (item.isChecked) {
    emits('updateParamsList')
  }
}
</script>

<style scoped lang="scss">
.retry-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}
</style>
