<template>
  <div>
    <!--TODO-todo: OpenAI：各种颜色icon-->
    <div v-if="currentProvider.icon_small" class="flex h-5 w-5 items-center justify-center">
      <img :src="renderI18nObject(currentProvider.icon_small)" alt="model-icon" />
    </div>
    <div
      v-else
      class="flex h-5 w-5 items-center justify-center rounded-md border-[0.5px] border-components-panel-border-subtle bg-background-default-subtle"
    >
      <div class="flex h-5 w-5 items-center justify-center opacity-35">
        <icon-play-arrow />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useProviderStore } from '@/stores/modules/workflow/provider'
import { renderI18nObject } from '@/utils/configuration'
import { type Model } from '@/apis/model-mgmt/type'

const providerStore = useProviderStore()
const currentProvider = ref({} as Model)
watch(
  () => providerStore.provider.currentProvider,
  () => {
    currentProvider.value = providerStore.provider.currentProvider
  }
)
</script>
<style scoped lang="scss"></style>
