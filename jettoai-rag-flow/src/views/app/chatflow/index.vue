<template>
  <div class="chatflow-container h-full flex">
    <!-- 左侧输入面板 -->
    <div class="input-panel w-96 border-r border-gray-200 flex flex-col">
      <!-- 头部信息 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div
            class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
          >
            <icon-robot class="text-white text-lg" />
          </div>
          <div>
            <h3 class="font-semibold text-gray-900">{{ appInfo?.name || 'Chatflow 应用' }}</h3>
            <p class="text-sm text-gray-500">{{ appInfo?.description || '智能对话流应用' }}</p>
          </div>
        </div>
      </div>

      <!-- 变量输入表单 -->
      <div class="flex-1 p-4 overflow-y-auto">
        <div v-if="variables.length > 0" class="space-y-4">
          <div class="text-sm font-medium text-gray-700 mb-3">输入变量</div>
          <div v-for="variable in variables" :key="variable.variable" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ variable.label }}
              <span v-if="variable.required" class="text-red-500">*</span>
            </label>

            <!-- 文本输入 -->
            <a-input
              v-if="variable.type === 'text-input'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :max-length="variable.max_length"
              show-word-limit
            />

            <!-- 段落输入 -->
            <a-textarea
              v-else-if="variable.type === 'paragraph'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :max-length="variable.max_length"
              :rows="4"
              show-word-limit
            />

            <!-- 选择器 -->
            <a-select
              v-else-if="variable.type === 'select'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
            >
              <a-option v-for="option in variable.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-option>
            </a-select>

            <!-- 数字输入 -->
            <a-input-number
              v-else-if="variable.type === 'number'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              class="w-full"
            />

            <!-- 单文件上传 -->
            <a-upload
              v-else-if="variable.type === 'file'"
              v-model:file-list="variableValues[variable.variable]"
              :limit="1"
              :auto-upload="false"
              :show-file-list="true"
              :show-upload-button="true"
              :show-retry-button="false"
              :show-cancel-button="false"
              accept="*"
            >
              <template #upload-button>
                <a-button>
                  <template #icon>
                    <icon-upload />
                  </template>
                  选择文件
                </a-button>
              </template>
            </a-upload>

            <!-- 多文件上传 -->
            <a-upload
              v-else-if="variable.type === 'file-list'"
              v-model:file-list="variableValues[variable.variable]"
              :auto-upload="false"
              :show-file-list="true"
              :show-upload-button="true"
              :show-retry-button="false"
              :show-cancel-button="false"
              multiple
              accept="*"
            >
              <template #upload-button>
                <a-button>
                  <template #icon>
                    <icon-upload />
                  </template>
                  选择文件
                </a-button>
              </template>
            </a-upload>

            <!-- URL 输入 -->
            <a-input
              v-else-if="variable.type === 'url'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              type="url"
            />

            <!-- JSON 输入 -->
            <a-textarea
              v-else-if="variable.type === 'json'"
              v-model="variableValues[variable.variable]"
              :placeholder="variable.label"
              :required="variable.required"
              :rows="6"
              show-word-limit
            />

            <div v-if="variable.description" class="text-xs text-gray-500">
              {{ variable.description }}
            </div>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 mt-8">
          <icon-info-circle class="text-4xl mb-2" />
          <p>此应用无需输入变量</p>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="p-4 border-t border-gray-200">
        <a-button type="primary" long :loading="isStarting" :disabled="!canStart" @click="startConversation">
          <template #icon>
            <icon-play-arrow />
          </template>
          开始对话
        </a-button>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-area flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <div class="chat-header p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <icon-message class="text-gray-600" />
            <span class="font-medium text-gray-900">对话</span>
          </div>
          <div class="flex items-center space-x-2">
            <a-button v-if="isExecuting" size="small" status="danger" :loading="stopLoading" @click="stopExecution">
              <template #icon>
                <icon-stop />
              </template>
              停止
            </a-button>
            <a-button size="small" @click="clearConversation">
              <template #icon>
                <icon-refresh />
              </template>
              清空
            </a-button>
          </div>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div ref="messagesContainer" class="chat-messages flex-1 overflow-y-auto p-4">
        <!-- <div v-if="messages.length === 0" class="text-center text-gray-500 mt-16">
          <icon-robot class="text-6xl mb-4 text-gray-300" />
          <p class="text-lg">欢迎使用 Chatflow</p>
          <p class="text-sm">请先填写左侧的输入变量，然后开始对话</p>
        </div> -->

        <ChatflowRoom
          :app-id="appId"
          :variables="variables"
          :is-running="isExecuting"
          :show-variable-form="true"
          @start-execution="handleStartExecution"
          @stop-execution="handleStopExecution"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { chatflowRunFetch } from '@/apis/workflow/chatflow'
import { getAppConfig } from '@/apis'
import { Message } from '@arco-design/web-vue'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import ChatflowRoom from '../workflow/run/chatflow-room.vue'
import { useAppShare } from '@/stores'
import { getAppParams, shareRun } from '@/apis/workflow/share'
import type { SiteInfo } from '@/views/app/workflow/types/share'
import { Resolution, TransferMethod, type VisionFile, type VisionSettings } from '@/views/app/workflow/types/app'
import type { PromptConfig } from '@/views/app/workflow/types/debug'
import { userInputsFormToPromptVariables } from '@/views/app/workflow/utils/model-config'
import type { MoreLikeThisConfig, TextToSpeechConfig } from '@/views/workspace/ai/share/type'

const { installedApps } = useAppShare()

defineOptions({ name: 'ChatflowPage' })

const route = useRoute()
const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)

// 从路由获取应用ID
const appId = computed(() => route.params.appId as string)

const isExecuting = ref(false)
const isStarting = ref(false)
const stopLoading = ref(false)
const conversationStarted = ref(false)
const messagesContainer = ref()
const variableValues = ref<Record<string, any>>({})
const siteInfo = ref<SiteInfo | null>(null)
const promptConfig = ref<PromptConfig | null>(null)
const moreLikeThisConfig = ref<MoreLikeThisConfig | null>(null)
const textToSpeechConfig = ref<TextToSpeechConfig | null>(null)
const visionConfig = ref<VisionSettings>({
  enabled: false,
  number_limits: 2,
  detail: Resolution.low,
  transfer_methods: [TransferMethod.local_file]
})

// 获取应用信息和变量
// const appInfo = computed(() => currentAppInfo.value)
const variables = computed(() => {
  // 这里需要从应用配置中获取变量信息
  return []
})

const messages = computed(() => [])

// 检查是否可以开始对话
const canStart = computed(() => {
  if (variables.value.length === 0) return true

  for (const variable of variables.value) {
    if (variable.required && !variableValues.value[variable.variable]) {
      return false
    }
  }
  return true
})

// 初始化应用信息和变量默认值
onMounted(async () => {
  // 获取应用配置
  try {
    const appConfig = await getAppConfig({ appId: appId.value })
    appStore.setCurrentAppInfo(appConfig)
  } catch (error) {
    console.error('获取应用配置失败:', error)
    Message.error('获取应用配置失败')
  }

  // 初始化变量默认值
  variables.value.forEach((variable) => {
    if (variable.default) {
      variableValues.value[variable.variable] = variable.default
    }
  })
})

const startConversation = async () => {
  if (!canStart.value) {
    Message.error('请填写所有必填变量')
    return
  }

  isStarting.value = true

  try {
    conversationStarted.value = true
    isExecuting.value = false
  } catch (error) {
    console.error('启动对话失败:', error)
    Message.error('启动对话失败')
  } finally {
    isStarting.value = false
  }
}

const handleStartExecution = () => {
  isExecuting.value = true
}

const handleStopExecution = () => {
  isExecuting.value = false
}

const stopExecution = async () => {
  stopLoading.value = true

  try {
    isExecuting.value = false
  } catch (error) {
    console.error('停止执行失败:', error)
  } finally {
    stopLoading.value = false
  }
}
const appInfo = ref()

onMounted(async () => {
  const appParams = await getAppParams((route.params.appId as string) || '')
  console.log(appParams)
  const currentApp = installedApps.find((ele) => ele.id === route.params.appId)
  console.log(currentApp, '--------')
  appInfo.value = currentApp
  const appData = {
    app_id: currentApp?.id || '',
    site: {
      title: currentApp?.app.name || '',
      prompt_public: false,
      copyright: '',
      icon: currentApp?.app.icon,
      icon_background: currentApp?.app.icon_background
    },
    plan: 'basic'
  }
  if (currentApp) {
    const { app_id, site } = appData
    siteInfo.value = site

    const { user_input_form, more_like_this, file_upload, text_to_speech }: any = appParams

    visionConfig.value = {
      ...file_upload,
      transfer_methods: file_upload.allowed_file_upload_methods || file_upload.allowed_upload_methods,
      image_file_size_limit: appParams?.system_parameters?.image_file_size_limit,
      fileUploadConfig: appParams?.system_parameters
    }

    const prompt_variables = userInputsFormToPromptVariables(user_input_form)

    promptConfig.value = {
      prompt_template: '',
      prompt_variables
    } as PromptConfig

    moreLikeThisConfig.value = more_like_this
    textToSpeechConfig.value = text_to_speech
  }
})

const clearConversation = () => {
  conversationStarted.value = false
  isExecuting.value = false
}
</script>

<style scoped lang="scss">
.chatflow-container {
  height: 100%;

  .input-panel {
    min-width: 384px;
    max-width: 384px;
  }
}
</style>
