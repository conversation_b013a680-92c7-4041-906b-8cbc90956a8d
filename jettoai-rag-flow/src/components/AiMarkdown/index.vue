<!-- MarkdownRenderer.vue -->
<template>
  <div class="markdown-body" v-html="compiledMarkdown" />
</template>

<script>
import { defineComponent, computed } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

// 初始化 markdown-it 实例
const md = new MarkdownIt({
  html: true, // 允许 HTML 标签
  linkify: true, // 自动转换 URL 为链接
  typographer: true, // 优化排版
  highlight: (str, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code>${
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value
        }</code></pre>`
      } catch (__) {}
    }
    return '' // 使用默认处理
  }
})

export default defineComponent({
  props: {
    content: {
      type: String,
      required: true
    }
  },

  setup(props) {
    const compiledMarkdown = computed(() => md.render(props.content))

    return { compiledMarkdown }
  }
})
</script>

<style>
@import 'highlight.js/styles/github.css';

.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  line-height: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 15px;
  overflow: hidden;
  white-space: normal;
  overflow-wrap: break-word;
}

/* 自定义 Markdown 样式 */
.markdown-body h1 {
  border-bottom: 2px solid #eee;
}

.markdown-body pre {
  padding: 15px;
  border-radius: 6px;
}

.markdown-body table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 12px;
  border-radius: 10px;
  display: block;
  overflow-x: auto;
  white-space: nowrap;
}

.markdown-body th {
  background: #f8f9fa;
  font-weight: 500;
  border-bottom: 1px solid #dee2e6;
}

.markdown-body td,
.markdown-body th {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
}

/* 响应式表格（可选） */
@media screen and (max-width: 768px) {
  .markdown-body table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}
</style>
