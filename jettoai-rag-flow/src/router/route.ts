import type { RouteRecordRaw } from 'vue-router'

/** 默认布局 */
const Layout = () => import('@/layout/index.vue')

/** 系统路由 */
export const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/',
    name: 'Workspace',
    component: Layout,
    redirect: '/workspace/ai',
    meta: { title: '工作空间', icon: 'menus-home', hidden: false },
    children: [
      {
        path: '/workspace/ai',
        name: 'Workplace-ai',
        component: () => import('@/views/workspace/ai/index.vue'),
        meta: { title: 'AI助手', icon: 'menus-message', hidden: false, activeMenu: '/workspace/ai' }
        // children: [
        //   {
        //     path: '/workspace/ai/apps',
        //     name: 'Workplace-apps',
        //     component: () => import('@/views/workspace/ai/app-store/index.vue'),
        //     meta: { title: '应用', icon: 'menus-message', hidden: true, affix: true },
        //     props: true
        //   },
        //   {
        //     path: '/workspace/ai/explore/:appId',
        //     name: 'Workplace-explore',
        //     component: () => import('@/views/workspace/ai/run/index.vue'),
        //     meta: { title: '运行', icon: 'menus-message', hidden: true, affix: true },
        //     props: true
        //   }
        // ]
      },
      {
        path: '/workspace/ai/explore/:appId',
        name: 'Workplace-explore',
        component: () => import('@/views/workspace/ai/run/index.vue'),
        meta: { title: '运行', icon: 'menus-message', hidden: true, affix: true },
        props: true
      }
      // {
      //   path: '/workspace/datasets',
      //   name: 'Workplace-datasets',
      //   component: () => import('@/views/workspace/datasets/index.vue'),
      //   meta: { title: '知识库', icon: 'menus-book', hidden: false, affix: true }
      // },
      // {
      //   path: '/workspace/evaluation',
      //   name: 'Workplace-evaluation',
      //   component: () => import('@/views/workspace/evaluation/index.vue'),
      //   meta: { title: '效果评价', icon: 'line-chart', hidden: false, affix: true },
      // },
      // {
      //   path: '/workspace/analytics',
      //   name: 'Workplace-analytics',
      //   component: () => import('@/views/workspace/analytics/index.vue'),
      //   meta: { title: '统计分析', icon: 'menus-insert-chart', hidden: false, affix: true }
      // }
    ]
  },
  {
    path: '/appsStore',
    component: Layout,
    redirect: '/appsStore/index',
    children: [
      {
        path: '/appsStore/index',
        name: 'AppsStore',
        component: () => import('@/views/apps-store/index.vue'),
        meta: { title: '应用市场', icon: 'menus-apps', hidden: false }
      }
    ]
  },
  {
    path: '/evaluation',
    component: Layout,
    redirect: '/evaluation/index',
    children: [
      {
        path: '/evaluation/index',
        name: 'Evaluation',
        component: () => import('@/views/evaluation/index.vue'),
        meta: { title: '评测管理', icon: 'menus-line-chart', hidden: false }
      },
      {
        path: '/evaluation/create-task',
        name: 'CreateEvaluationTask',
        component: () => import('@/views/evaluation/components/task/CreateTask.vue'),
        meta: { title: '创建评测任务', hidden: true, activeMenu: '/evaluation/index' }
      }
    ]
  },
  {
    path: '/ai-assistant',
    component: Layout,
    redirect: '/ai-assistant/index',
    children: [
      {
        path: '/ai-assistant/index',
        name: 'AiAssistant',
        component: () => import('@/views/app/ai-assistant/index.vue'),
        meta: { title: 'AI助手', icon: 'menus-robot', hidden: false }
      }
    ]
  },
  {
    path: '/appsManage',
    component: Layout,
    redirect: '/appsManage/index',
    meta: { hidden: false },
    children: [
      {
        path: '/appsManage/index',
        name: 'AppsManage',
        component: () => import('@/views/apps-manage/index.vue'),
        meta: { title: '设计空间', icon: 'menus-common', hidden: false }
      },
      {
        path: '/appsManage/flow/:appId/:appType',
        name: 'Flow',
        component: () => import('@/views/app/index.vue'),
        meta: { title: '流程编排', icon: 'menus-common', hidden: true, activeMenu: '/appsManage/index' }
      },
      {
        path: '/appsManage/chatflow/:appId',
        name: 'Chatflow',
        component: () => import('@/views/app/chatflow/index.vue'),
        meta: { title: 'Chatflow 运行', icon: 'menus-message', hidden: true, activeMenu: '/appsManage/index' }
      }
    ]
  },
  {
    path: '/datasets',
    component: Layout,
    redirect: '/datasets/index',
    meta: { hidden: false },
    children: [
      {
        path: '/datasets/index',
        name: 'Datasets',
        component: () => import('@/views/datasets/index.vue'),
        meta: { title: '知识库', icon: 'menus-book', hidden: false }
      },
      {
        path: '/datasets/create',
        name: 'CreateDataset',
        component: () => import('@/views/datasets/create/index.vue'),
        meta: { title: '创建知识库', hidden: true, activeMenu: '/datasets/index' }
      },
      {
        path: '/datasets/:datasetId/documents',
        name: 'documents',
        component: () => import('@/views/datasets/documents/index.vue'),
        meta: { title: '知识库文档', hidden: true, activeMenu: '/datasets/index' }
      },
      {
        path: '/datasets/:datasetId/chunk-setting',
        name: 'documentsChunkSetting',
        component: () => import('@/views/datasets/documents/DocumentsChunkSetting.vue'),
        meta: { title: '文档分段设置', hidden: true, activeMenu: '/datasets/index' }
      },
      {
        path: '/datasets/:datasetId/chunk-detail/:documentId',
        name: 'documentsChunkDetail',
        component: () => import('@/views/datasets/documents/DocumentsChunkSettingDetail.vue'),
        meta: { title: '分块详情', hidden: true, activeMenu: '/datasets/index' }
      }
    ]
  },
  {
    path: '/modelMgmt',
    component: Layout,
    redirect: '/modelMgmt/index',
    meta: { hidden: false },
    children: [
      {
        path: '/modelMgmt/index',
        name: 'ModelMgmt',
        component: () => import('@/views/model-mgmt/index.vue'),
        meta: { title: '模型管理', icon: 'menus-bulb', hidden: false }
      }
    ]
  },
  {
    path: '/prompts',
    component: Layout,
    redirect: '/prompts/index',
    meta: { hidden: false },
    children: [
      {
        path: '/prompts/index',
        name: 'Prompts',
        component: () => import('@/views/prompts/index.vue'),
        meta: { title: '提示词管理', icon: 'menus-message', hidden: false }
      },
      {
        path: '/prompts/create',
        name: 'CreatePrompt',
        component: () => import('@/views/prompts/components/PromptAddPage.vue'),
        meta: { title: '新增提示词', hidden: true, activeMenu: '/prompts/index' }
      },
      {
        path: '/prompts/edit/:id',
        name: 'EditPrompt',
        component: () => import('@/views/prompts/components/PromptAddPage.vue'),
        meta: { title: '编辑提示词', hidden: true, activeMenu: '/prompts/index' }
      }
    ]
  },
  {
    path: '/chat/:id',
    name: 'Chat',
    component: () => import('@/views/chat/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pwdExpired',
    component: () => import('@/views/login/pwdExpired/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/userMgmt',
    component: Layout,
    redirect: '/userMgmt/index',
    meta: { hidden: false },
    children: [
      {
        path: '/userMgmt/index',
        name: 'UserMgmt',
        component: () => import('@/views/user-mgmt/index.vue'),
        meta: { title: '用户管理', icon: 'menus-user', hidden: false }
      }
    ]
  }
  // {
  //   path: '/template',
  //   component: Layout,
  //   redirect: '/template/index',
  //   meta: { hidden: false },
  //   children: [
  //     {
  //       path: '/template/index',
  //       name: 'Template',
  //       component: () => import('@/template/CommonTabs.vue'),
  //       meta: { title: '模版管理', icon: 'menus-bulb', hidden: true }
  //     }
  //   ]
  // }
]

// 固定路由（默认路由）
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/default/redirect/index.vue')
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/default/error/404.vue'),
    meta: { hidden: true }
  },
  {
    path: '/403',
    component: () => import('@/views/default/error/403.vue'),
    meta: { hidden: true }
  }
]
